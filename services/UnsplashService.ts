import auth from '@react-native-firebase/auth';

/**
 * Response type for Unsplash image requests
 */
export interface UnsplashImageResponse {
  success: boolean;
  imageUrl?: string;
  error?: string;
  statusCode?: number;
}

/**
 * Service for fetching images from Unsplash via secure cloud function
 */
export class UnsplashService {
  private static readonly CLOUD_FUNCTION_URL =
    'https://us-central1-chefpal-a9abe.cloudfunctions.net/get_unsplash_image';

  /**
   * Fetches an image URL from Unsplash for the given search query
   * @param query - Search query for the image
   * @returns Promise<UnsplashImageResponse> - Response object with success status and either imageUrl or error
   */
  static async getImageUrl(query: string): Promise<UnsplashImageResponse> {
    try {
      // Get the current user's ID token for authentication
      const user = auth().currentUser;
      if (!user) {
        return {
          success: false,
          error: 'User not authenticated',
          statusCode: 401,
        };
      }

      const idToken = await user.getIdToken();

      // Call the cloud function
      const response = await fetch(`${this.CLOUD_FUNCTION_URL}?query=${encodeURIComponent(query)}`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${idToken}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.error || `HTTP ${response.status}: ${response.statusText}`,
          statusCode: response.status,
        };
      }

      if (!data.imageUrl) {
        return {
          success: false,
          error: 'No image URL in response',
          statusCode: response.status,
        };
      }

      return {
        success: true,
        imageUrl: data.imageUrl,
      };
    } catch (error) {
      console.error('Error fetching Unsplash image:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        statusCode: 500,
      };
    }
  }
}
