import React, { useState } from 'react';
import { useRouter } from 'expo-router';
import LoadingAnimation from '@/components/LoadingAnimation';
import CaptureView from '@/components/CaptureView';
import { analyzeImagesAsync } from '@/services/analyzeImages';
import { InventoryService } from '@/services/InventoryService';
import { useInventory } from '@/contexts/InventoryContext';
import { InventoryItem } from '@/components/types';

export default function CameraScreen() {
  const router = useRouter();
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const { updateInventoryWithCategories } = useInventory();

  const handlePhotosTaken = async (photos: string[]) => {
    setIsAnalyzing(true);
    try {
      const ingredients = await analyzeImagesAsync(photos);

      // Convert ingredients to InventoryItem format
      const newItems: InventoryItem[] = ingredients.map((ingredient) => ({
        name: ingredient.name,
        quantity: 1, // Default quantity
      }));

      // Prepare the updated inventory locally first
      const currentInventory = await InventoryService.getUserInventory();
      const updatedInventory = [...currentInventory];
      const currentTime = Date.now();

      // Process new items locally to get the final inventory state
      for (const newItem of newItems) {
        const existingItemIndex = updatedInventory.findIndex(
          (i) => i.name.toLowerCase() === newItem.name.toLowerCase()
        );

        if (existingItemIndex >= 0) {
          // Update existing item - only update quantity if current is 0
          if (updatedInventory[existingItemIndex].quantity === 0) {
            updatedInventory[existingItemIndex] = {
              ...updatedInventory[existingItemIndex],
              quantity: newItem.quantity,
            };
          }
        } else {
          // Add new item
          updatedInventory.push({
            ...newItem,
            addedAt: currentTime,
          });
        }
      }

      // Run backend update and LLM categorization in parallel
      const [, categorizedInventory] = await Promise.all([
        InventoryService.addOrUpdateMultipleItems(newItems),
        InventoryService.categorizeInventory(updatedInventory),
      ]);

      // Update local state with properly categorized inventory
      // Use the new function that accepts both items and categories to avoid re-categorization
      updateInventoryWithCategories(updatedInventory, categorizedInventory);

      // Navigate back to inventory tab
      router.push('/(tabs)/inventory');
    } catch (error) {
      console.error('Error analyzing photos:', error);
      // TODO: Show error message to user
      router.back();
    } finally {
      setIsAnalyzing(false);
    }
  };

  if (isAnalyzing) {
    return (
      <LoadingAnimation
        source={require('../assets/images/gifs/bounce-veggie.gif')}
        message='Analyzing and organizing your food items...'
      />
    );
  }

  return <CaptureView onPhotosTaken={handlePhotosTaken} onClose={() => router.push('/(tabs)/inventory')} />;
}