import React from 'react';
import { View, Text, StyleSheet, Pressable, ScrollView, useColorScheme } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { getThemeColors, ThemeColors } from '@/styles/Theme';

interface OnboardingSummaryCardProps {
    answers: Record<string, string | string[]>;
    onProceed: () => void;
}

const answerLabels: Record<string, string> = {
    allergies: 'Food Allergies',
    cooking_time: 'Cooking Time Preference',
    cooking_experience: 'Cooking Experience',
    calorie_goal: 'Daily Calorie Goal',
    specific_diet: 'Dietary Preferences',
};

const formatAnswer = (answer: string | string[]): string => {
    if (Array.isArray(answer)) {
        return answer.map(a => a.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())).join(', ');
    }
    return answer.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

export const OnboardingSummaryCard: React.FC<OnboardingSummaryCardProps> = ({ answers, onProceed }) => {
    const colorScheme = useColorScheme() || 'light';
    const colors = getThemeColors(colorScheme);
    const styles = createSummaryCardStyles(colors);

    return (
      <SafeAreaView style={styles.safeAreaContainer}>
        <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
          <Text style={styles.title}>Onboarding Summary</Text>
          <Text style={styles.subTitle}>Here's a summary of your preferences:</Text>

          {Object.entries(answers).map(([key, value]) => (
            <View key={key} style={styles.summaryItem}>
              <Text style={styles.itemLabel}>
                {answerLabels[key] || key.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())}:
              </Text>
              <Text style={styles.itemValue}>{formatAnswer(value)}</Text>
            </View>
          ))}
          <Pressable style={styles.proceedButton} onPress={onProceed}>
            <Text style={styles.proceedButtonText}>Done</Text>
          </Pressable>
        </ScrollView>
      </SafeAreaView>
    );
};

const createSummaryCardStyles = (colors: ThemeColors) => StyleSheet.create({
    safeAreaContainer: {
        flex: 1,
        backgroundColor: colors.background,
    },
    container: {
        flex: 1,
        backgroundColor: colors.background,
    },
    contentContainer: {
        padding: 24,
        alignItems: 'center',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        color: colors.text,
        marginBottom: 16,
        textAlign: 'center',
    },
    subTitle: {
        fontSize: 16,
        color: colors.textSecondary,
        marginBottom: 24,
        textAlign: 'center',
    },
    summaryItem: {
        backgroundColor: colors.surface, // Use surface for card-like items
        borderRadius: 8,
        padding: 16,
        marginBottom: 12,
        width: '100%',
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05, // Softer shadow
        shadowRadius: 2,
        elevation: 1,
    },
    itemLabel: {
        fontSize: 16,
        fontWeight: '600',
        color: colors.text,
        marginBottom: 6,
    },
    itemValue: {
        fontSize: 16,
        color: colors.textSecondary,
    },
    nextStepText: {
        fontSize: 15,
        color: colors.text,
        textAlign: 'center',
        marginVertical: 30,
        lineHeight: 22,
    },
    proceedButton: {
        backgroundColor: colors.accent,
        paddingVertical: 14,
        paddingHorizontal: 30,
        borderRadius: 8,
        marginTop: 20,
        width: '80%',
        alignItems: 'center',
    },
    proceedButtonText: {
        color: colors.accentText,
        fontSize: 18,
        fontWeight: 'bold',
    },
}); 