import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import auth, { FirebaseAuthTypes } from '@react-native-firebase/auth';
import { usersCollection } from '@/firebase/firebaseConfig';

interface AuthContextType {
  user: FirebaseAuthTypes.User | null;
  loading: boolean;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<FirebaseAuthTypes.User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Sign in anonymously if no user is present
    const initializeAuth = async () => {
      try {
        if (!auth().currentUser) {
          await auth().signInAnonymously();
        }
      } catch (error) {
        console.error('Anonymous sign-in error:', error);
        setLoading(false);
      }
    };

    const unsubscribe = auth().onAuthStateChanged(async (currentUser) => {
      setUser(currentUser);
      const uid = currentUser?.uid;
      setLoading(false);
      if (uid) {
        await usersCollection.doc(uid).set(
          {
            lastActiveAt: new Date(),
          },
          { merge: true }
        );
      }
    });

    initializeAuth();

    return () => unsubscribe();
  }, []);

  const value = {
    user,
    loading,
    isAuthenticated: !!user,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
