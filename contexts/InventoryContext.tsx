import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo } from 'react';
import { InventoryItem, InventoryCategory } from '@/components/types';
import { InventoryService } from '@/services/InventoryService';
import { useAuth } from '@/contexts/AuthContext';
import Fuse from 'fuse.js';

interface InventoryContextType {
  inventoryItems: InventoryItem[];
  categorizedInventory: InventoryCategory[];
  loading: boolean;
  isIngredientAvailable: (ingredientName: string) => boolean;
  refreshInventory: () => Promise<void>;
  updateLocalInventory: (items: InventoryItem[]) => void;
  updateInventoryWithCategories: (items: InventoryItem[], categories: InventoryCategory[]) => void;
}

const InventoryContext = createContext<InventoryContextType | undefined>(undefined);

export const useInventory = () => {
  const context = useContext(InventoryContext);
  if (!context) {
    throw new Error('useInventory must be used within an InventoryProvider');
  }
  return context;
};

interface InventoryProviderProps {
  children: ReactNode;
}

export const InventoryProvider: React.FC<InventoryProviderProps> = ({ children }) => {
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [categorizedInventory, setCategorizedInventory] = useState<InventoryCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const { isAuthenticated } = useAuth();

  // Create Fuse instance for fuzzy searching inventory items
  // This enables smart matching like:
  // - "tomato" matches "Tomatoes"
  // - "olive oil" matches "Extra virgin olive oil"
  // - "chicken breast" matches "Chicken breasts"
  // - "milk" matches "Almond milk" or "Whole milk"
  const fuse = useMemo(() => {
    const options = {
      keys: ['name'],
      threshold: 0.4, // Lower threshold = more strict matching (0.0 = exact match, 1.0 = match anything). I.e. "Arugula" → "Asparagus" (score: 0.429)
      distance: 100, // Maximum distance for a match
      includeScore: true,
      ignoreLocation: true, // Don't consider position of match in string
      minMatchCharLength: 2, // Minimum character length for a match
    };

    // Only include items with quantity > 0
    const availableItems = inventoryItems.filter((item) => item.quantity > 0);
    return new Fuse(availableItems, options);
  }, [inventoryItems]);

  // Load inventory when authentication state changes
  useEffect(() => {
    if (isAuthenticated) {
      refreshInventory();
    } else {
      setInventoryItems([]);
      setCategorizedInventory([]);
    }
  }, [isAuthenticated]);

  // Function to check if an ingredient is available in inventory using fuzzy search
  const isIngredientAvailable = (ingredientName: string): boolean => {
    if (!ingredientName || !inventoryItems.length) return false;

    // First try exact match (case-insensitive)
    const exactMatch = inventoryItems.find(
      (item) => item.name.toLowerCase().trim() === ingredientName.toLowerCase().trim() && item.quantity > 0
    );

    if (exactMatch) {
      console.log(`🎯 Exact match found: "${ingredientName}" → "${exactMatch.name}"`);
      return true;
    }

    // If no exact match, try fuzzy search
    const results = fuse.search(ingredientName.trim());

    // Return true if we found at least one good match
    if (results.length > 0 && results[0].score !== undefined && results[0].score < 0.5) {
      console.log(
        `🔍 Fuzzy match found: "${ingredientName}" → "${results[0].item.name}" (score: ${results[0].score?.toFixed(3)})`
      );
      return true;
    }

    console.log(`❌ No match found for: "${ingredientName}"`);
    return false;
  };

  // Load inventory from Firestore
  const refreshInventory = async () => {
    try {
      setLoading(true);
      const items = await InventoryService.getUserInventory();
      setInventoryItems(items);

      if (items.length > 0) {
        const categories = await InventoryService.categorizeInventory(items);
        setCategorizedInventory(categories);
      } else {
        setCategorizedInventory([]);
      }
    } catch (error) {
      console.error('Error loading inventory:', error);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to get emoji for a category
  const getCategoryEmoji = (categoryName: string): string => {
    const categoryEmojis: Record<string, string> = {
      Fruits: '🍓',
      Vegetables: '🥦',
      Meat: '🥩',
      'Grains & Cereals': '🌾',
      'Dairy & Alternatives': '🥛',
      'Fats & Oils': '🫒',
      'Sweets & Snacks': '🍪',
      'Condiments & Spices': '🧂',
      Drinks: '🥤',
      Other: '🍥',
    };
    return categoryEmojis[categoryName] || '🍥';
  };

  // Update local inventory state (for optimistic updates)
  const updateLocalInventory = (updatedItems: InventoryItem[]) => {
    setInventoryItems(updatedItems);

    if (updatedItems.length === 0) {
      setCategorizedInventory([]);
      return;
    }

    // Immediately update categorized inventory using existing categories
    const updatedCategories = categorizedInventory
      .map((category) => {
        // Update existing items and remove deleted ones
        const updatedCategoryItems = category.items
          .map((item: InventoryItem) => {
            const updatedItem = updatedItems.find((i) => i.name === item.name);
            return updatedItem || item;
          })
          .filter((item: InventoryItem) => {
            // Keep only items that still exist in the updated inventory
            return updatedItems.some((i) => i.name === item.name);
          });

        return {
          ...category,
          items: updatedCategoryItems,
        };
      })
      .filter((category) => category.items.length > 0); // Remove empty categories

    // Find new items that need to be added to existing categories
    const existingItemNames = updatedCategories.flatMap((category) =>
      category.items.map((item: InventoryItem) => item.name)
    );
    const newItems = updatedItems.filter((item) => !existingItemNames.includes(item.name));

    // Add new items to appropriate categories (use existing category or create new one)
    if (newItems.length > 0) {
      for (const newItem of newItems) {
        const categoryName = newItem.category || 'Other';
        let categoryIndex = updatedCategories.findIndex((c) => c.name === categoryName);

        if (categoryIndex >= 0) {
          // Add to existing category
          updatedCategories[categoryIndex].items.push(newItem);
        } else {
          // Create a new category
          const emoji = getCategoryEmoji(categoryName);
          updatedCategories.push({
            name: categoryName,
            emoji: emoji,
            items: [newItem],
          });
        }
      }
    }

    // Update the categorized inventory immediately
    setCategorizedInventory(updatedCategories);

    // Re-categorize with LLM in the background for proper categorization
    InventoryService.categorizeInventory(updatedItems)
      .then((categories) => setCategorizedInventory(categories))
      .catch((error) => console.error('Error re-categorizing inventory:', error));
  };

  // Update inventory with pre-categorized data (used when we already have categories from LLM)
  const updateInventoryWithCategories = (updatedItems: InventoryItem[], categories: InventoryCategory[]) => {
    setInventoryItems(updatedItems);
    setCategorizedInventory(categories);
  };

  const value = {
    inventoryItems,
    categorizedInventory,
    loading,
    isIngredientAvailable,
    refreshInventory,
    updateLocalInventory,
    updateInventoryWithCategories,
  };

  return <InventoryContext.Provider value={value}>{children}</InventoryContext.Provider>;
};
